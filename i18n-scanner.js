const fs = require('fs');
const path = require('path');

// 中文字符正则表达式
const CHINESE_REGEX = /[\u4e00-\u9fff]+/g;

// 路由映射表（从router.js提取）
const ROUTE_MAPPING = {
    'analyseConfig': 'index-analyseConfig',
    'analyseData': 'index-analyseData', 
    'appMonitor': 'index-appMonitor',
    'businessMonitor': 'index-businessMonitor',
    'ldpDataObservation': 'index-ldpDataObservation',
    'ldpLinkConfig': 'index-ldpLinkConfig',
    'ldpTable': 'index-ldpTable',
    'sqlTable': 'index-sqlTable',
    'sqlCores': 'index-sqlCores',
    'managementQuery': 'index-managementQuery',
    'mdbDataObservation': 'index-mdbDataObservation',
    'marketAllLink': 'index-marketAllLink',
    'marketMonitor': 'index-marketMonitor',
    'marketNodeDelayList': 'index-marketNodeDelayList',
    'marketPenetrateList': 'index-marketPenetrateList',
    'marketTimeDelay': 'index-marketTimeDelay',
    'monitor': 'index-monitor',
    'ldpMonitor': 'ldp-monitor',
    'noticeManagerList': 'index-noticeManagerList',
    'productDataStorage': 'index-productDataStorage',
    'productTimeDetail': 'index-productTimeDetail',
    'productTimeSummary': 'index-productTimeSummary',
    'smsList': 'index-smsList',
    'transaction': 'index-transaction',
    'rcmDeploy': 'index-rcmDeploy',
    'rcmBacklogMonitor': 'index-rcmBacklogMonitor',
    'mcDeploy': 'index-mcDeploy',
    'mcDataObservation': 'index-mcDataObservation',
    'brokerDataLimit': 'index-brokerDataLimit',
    'ustTableVerification': 'index-ustTableVerification',
    'coreReplayObservation': 'index-ccoreReplayObservation',
    'mdbPrivilegeManage': 'index-mdbPrivilegeManage',
    'networkSendAndRecevied': 'index-networkSendAndRecevied',
    'productServiceList': 'index-productServiceList',
    'tripartiteServiceList': 'index-tripartiteServiceList',
    'latencyTrendAnalysis': 'index-latencyTrendAnalysis',
    'topoMonitor': 'index-topoMonitor',
    'appRunningState': 'index-appRunningState',
    'accordMonitor': 'index-accordMonitor',
    'accordObservation': 'index-accordObservation',
    'rcmObservation': 'index-rcmObservation',
    'locateConfig': 'index-locateConfig',
    'ldpLogCenter': 'index-ldpLogCenter',
    'threadInfoOverview': 'index-threadInfoOverview',
    'coreFuncHandleObservation': 'index-coreFuncHandleObservation',
    'createRule': 'index-createRule',
    'emergencyManagementConfig': 'index-emergencyManagementConfig',
    'dataSecondAppearance': 'index-dataSecondAppearance',
    'mdbDataExport': 'index-mdbDataExport',
    'createMdbDataExport': 'index-createMdbDataExport'
};

// 扫描结果
const scanResults = {};

// 递归扫描目录
function scanDirectory(dirPath, basePath = '') {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            // 递归扫描子目录
            scanDirectory(filePath, path.join(basePath, file));
        } else if (file.endsWith('.vue')) {
            // 扫描Vue文件
            scanVueFile(filePath, basePath, file);
        }
    });
}

// 扫描Vue文件中的中文文案
function scanVueFile(filePath, basePath, fileName) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.join(basePath, fileName).replace(/\\/g, '/');
    
    // 提取路由名称
    const routeName = getRouteNameFromPath(relativePath);
    
    if (!scanResults[routeName]) {
        scanResults[routeName] = {
            path: relativePath,
            texts: new Set()
        };
    }
    
    // 提取中文文案
    extractChineseTexts(content, scanResults[routeName].texts);
}

// 从文件路径提取路由名称
function getRouteNameFromPath(filePath) {
    // 处理views/index下的文件
    if (filePath.startsWith('views/index/')) {
        const fileName = path.basename(filePath, '.vue');
        return ROUTE_MAPPING[fileName] || fileName;
    }
    
    // 处理components下的文件
    if (filePath.startsWith('components/')) {
        const parts = filePath.split('/');
        return parts[1] || 'common';
    }
    
    return 'common';
}

// 提取中文文案
function extractChineseTexts(content, textSet) {
    // 1. 匹配Vue模板中的纯文本中文
    const templateTextMatches = content.match(/>([^<{]*[\u4e00-\u9fff][^<{]*)</g);
    if (templateTextMatches) {
        templateTextMatches.forEach(match => {
            const text = match.replace(/^>|<$/g, '').trim();
            if (text && CHINESE_REGEX.test(text) && !text.includes('{{') && !text.includes('}}')) {
                const cleanText = text.replace(/\s+/g, ' ').trim();
                if (cleanText.length > 0 && cleanText.length < 100) {
                    textSet.add(cleanText);
                }
            }
        });
    }

    // 2. 匹配Vue属性中的中文（placeholder, title, label等）
    const attrMatches = content.match(/(placeholder|title|label|content|text|alt|aria-label)=["']([^"']*[\u4e00-\u9fff][^"']*)["']/gi);
    if (attrMatches) {
        attrMatches.forEach(match => {
            const text = match.replace(/^[^=]*=["']|["']$/g, '').trim();
            if (text && CHINESE_REGEX.test(text) && text.length < 100) {
                textSet.add(text);
            }
        });
    }

    // 3. 匹配JavaScript/TypeScript字符串中的中文（排除注释和长代码）
    const jsStringMatches = content.match(/['"`]([^'"`\n\r]*[\u4e00-\u9fff][^'"`\n\r]*)['"`]/g);
    if (jsStringMatches) {
        jsStringMatches.forEach(match => {
            const text = match.replace(/^['"`]|['"`]$/g, '').trim();
            if (text && CHINESE_REGEX.test(text) &&
                !text.includes('{{') &&
                !text.includes('${') &&
                !text.includes('import') &&
                !text.includes('export') &&
                !text.includes('function') &&
                !text.includes('console.') &&
                !text.includes('localStorage') &&
                !text.includes('this.$') &&
                text.length < 50 &&
                text.length > 1) {
                textSet.add(text);
            }
        });
    }

    // 4. 匹配Vue插值表达式中的中文字符串
    const interpolationMatches = content.match(/\{\{\s*['"`]([^'"`]*[\u4e00-\u9fff][^'"`]*)['"`]\s*\}\}/g);
    if (interpolationMatches) {
        interpolationMatches.forEach(match => {
            const text = match.replace(/\{\{\s*['"`]|['"`]\s*\}\}/g, '').trim();
            if (text && CHINESE_REGEX.test(text) && text.length < 50) {
                textSet.add(text);
            }
        });
    }

    // 5. 匹配对象属性值中的中文
    const objectPropMatches = content.match(/:\s*['"`]([^'"`]*[\u4e00-\u9fff][^'"`]*)['"`]/g);
    if (objectPropMatches) {
        objectPropMatches.forEach(match => {
            const text = match.replace(/^:\s*['"`]|['"`]$/g, '').trim();
            if (text && CHINESE_REGEX.test(text) &&
                text.length < 50 &&
                text.length > 1 &&
                !text.includes('http') &&
                !text.includes('www.') &&
                !text.includes('.com')) {
                textSet.add(text);
            }
        });
    }
}

// 生成国际化键值对
function generateI18nKeys(routeName, texts) {
    const keys = {};
    const textArray = Array.from(texts);
    
    textArray.forEach((text, index) => {
        // 生成键名
        const key = generateKeyName(text, index);
        keys[key] = text;
    });
    
    return keys;
}

// 生成键名
function generateKeyName(text, index) {
    // 移除特殊字符，转换为驼峰命名
    let cleanText = text
        .replace(/[^\u4e00-\u9fff\w\s]/g, '') // 移除特殊字符
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim();

    // 根据文本内容生成语义化的键名
    const keywordMap = {
        '请选择': 'pleaseSelect',
        '请输入': 'pleaseInput',
        '请输入搜索内容': 'pleaseInputSearchContent',
        '查询': 'query',
        '搜索': 'search',
        '重置': 'reset',
        '导出': 'export',
        '删除': 'delete',
        '编辑': 'edit',
        '保存': 'save',
        '取消': 'cancel',
        '确认': 'confirm',
        '提交': 'submit',
        '关闭': 'close',
        '返回': 'back',
        '刷新': 'refresh',
        '清空': 'clear',
        '选择': 'select',
        '全选': 'selectAll',
        '配置': 'config',
        '管理': 'management',
        '监控': 'monitor',
        '观测': 'observation',
        '数据': 'data',
        '应用': 'application',
        '节点': 'node',
        '实例': 'instance',
        '产品': 'product',
        '核心': 'core',
        '集群': 'cluster',
        '链路': 'link',
        '日志': 'log',
        '时间': 'time',
        '状态': 'status',
        '成功': 'success',
        '失败': 'failed',
        '错误': 'error',
        '警告': 'warning',
        '信息': 'info',
        '加载中': 'loading',
        '暂无数据': 'noData',
        '操作成功': 'operationSuccess',
        '操作失败': 'operationFailed',
        '开始时间': 'startTime',
        '结束时间': 'endTime',
        '时间范围': 'timeRange',
        '今天': 'today',
        '昨天': 'yesterday',
        '本周': 'thisWeek',
        '本月': 'thisMonth',
        '详情': 'detail',
        '添加': 'add',
        '新建': 'create',
        '修改': 'modify',
        '更新': 'update',
        '下载': 'download',
        '上传': 'upload',
        '导入': 'import',
        '批量': 'batch',
        '快捷': 'quick',
        '总览': 'overview',
        '列表': 'list',
        '表格': 'table',
        '图表': 'chart',
        '统计': 'statistics',
        '分析': 'analysis',
        '报告': 'report',
        '设置': 'settings',
        '帮助': 'help',
        '关于': 'about'
    };

    // 查找匹配的关键词
    for (const [keyword, key] of Object.entries(keywordMap)) {
        if (cleanText.includes(keyword)) {
            return key;
        }
    }

    // 如果没有匹配的关键词，生成基于内容的键名
    if (cleanText.length <= 10) {
        // 短文本直接转拼音或使用描述性名称
        return `text_${cleanText.replace(/\s/g, '_')}`;
    }

    // 长文本使用索引
    return `text${index + 1}`;
}

// 主函数
function main() {
    console.log('开始扫描项目中的中文文案...');
    
    // 扫描views目录
    if (fs.existsSync('./src/views')) {
        scanDirectory('./src/views', 'views');
    }
    
    // 扫描components目录
    if (fs.existsSync('./src/components')) {
        scanDirectory('./src/components', 'components');
    }
    
    // 生成报告
    const report = generateReport();
    
    // 输出结果
    fs.writeFileSync('./i18n-scan-report.json', JSON.stringify(report, null, 2), 'utf-8');
    console.log('扫描完成！结果已保存到 i18n-scan-report.json');
    
    // 输出统计信息
    console.log(`\n扫描统计：`);
    console.log(`- 扫描的路由页面数: ${Object.keys(report.routes).length}`);
    console.log(`- 发现的中文文案总数: ${report.summary.totalTexts}`);
    console.log(`- 生成的国际化键值对数: ${report.summary.totalKeys}`);
}

// 生成报告
function generateReport() {
    const routes = {};
    let totalTexts = 0;
    let totalKeys = 0;
    
    Object.keys(scanResults).forEach(routeName => {
        const result = scanResults[routeName];
        const texts = Array.from(result.texts);
        const i18nKeys = generateI18nKeys(routeName, result.texts);
        
        routes[routeName] = {
            path: result.path,
            texts: texts,
            i18nKeys: i18nKeys,
            count: texts.length
        };
        
        totalTexts += texts.length;
        totalKeys += Object.keys(i18nKeys).length;
    });
    
    return {
        summary: {
            totalRoutes: Object.keys(routes).length,
            totalTexts: totalTexts,
            totalKeys: totalKeys,
            scanTime: new Date().toISOString()
        },
        routes: routes
    };
}

// 运行扫描
if (require.main === module) {
    main();
}

module.exports = {
    scanDirectory,
    scanVueFile,
    extractChineseTexts,
    generateI18nKeys,
    generateReport
};
