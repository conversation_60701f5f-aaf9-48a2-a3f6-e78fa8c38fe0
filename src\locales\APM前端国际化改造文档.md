## 概述
本文档详细介绍了 LDP APM Query UI 项目的国际化(i18n)实施方案，包括配置、使用方法。

## 改造范围评估
### 路由页面统计
当前项目共有 **50 个路由页面** 需要进行国际化改造，具体分类如下：

#### 主要路由结构
+ **根路由** (`/`) - 使用动态布局组件
+ **主页面** (`index`) - 包含 50 个子路由页面

#### 功能模块分类
**监控类页面 (15 个)**

+ `appMonitor` - 应用监控
+ `businessMonitor` - 业务监控
+ `ldpMonitor` - LDP 监控 (包含子路由)
+ `marketMonitor` - 市场监控
+ `topoMonitor` - 拓扑监控
+ `accordMonitor` - 协议监控
+ `rcmBacklogMonitor` - RCM 积压监控
+ `ldpAppMonitor` - LDP 应用监控 (子路由)
+ `clusterMonitor` - 集群监控 (子路由)
+ `monitor` - 监控主页 (包含子路由)
+ 其他监控相关页面...

**数据观测类页面 (8 个)**

+ `ldpDataObservation` - LDP 数据观测
+ `mdbDataObservation` - MDB 数据观测
+ `mcDataObservation` - MC 数据观测
+ `accordObservation` - 协议观测
+ `rcmObservation` - RCM 观测
+ `coreFuncHandleObservation` - 核心功能处理观测
+ `coreReplayObservation` - 核心重放观测
+ `dataSecondAppearance` - 数据二次出现

**配置管理类页面 (6 个)**

+ `analyseConfig` - 分析配置
+ `ldpLinkConfig` - LDP 链路配置
+ `locateConfig` - 定位配置
+ `emergencyManagementConfig` - 应急管理配置
+ `createRule` - 创建规则
+ `mdbPrivilegeManage` - MDB 权限管理

**数据查询类页面 (7 个)**

+ `analyseData` - 分析数据
+ `ldpTable` - LDP 表格
+ `sqlTable` - SQL 表格
+ `sqlCores` - SQL 核心
+ `managementQuery` - 管理查询
+ `latencyTrendAnalysis` - 延迟趋势分析
+ `ustTableVerification` - UST 表验证

**部署运维类页面 (4 个)**

+ `rcmDeploy` - RCM 部署
+ `mcDeploy` - MC 部署
+ `appRunningState` - 应用运行状态
+ `threadInfoOverview` - 线程信息概览

**市场数据类页面 (5 个)**

+ `marketAllLink` - 市场全链路
+ `marketNodeDelayList` - 市场节点延迟列表
+ `marketPenetrateList` - 市场穿透列表
+ `marketTimeDelay` - 市场时间延迟
+ `networkSendAndRecevied` - 网络收发

**其他功能页面 (6 个)**

+ `noticeManagerList` - 通知管理列表
+ `smsList` - 短信列表
+ `transaction` - 交易
+ `productServiceList` - 产品服务列表
+ `tripartiteServiceList` - 三方服务列表
+ `ldpLogCenter` - LDP 日志中心

## 改造工作量评估（待确认）
<font style="color:#DF2A3F;">应用观测类翻译依靠平台提供字典（未知）</font>

| 工作项 | 工作量 | 说明 |
| --- | --- | --- |
| 页面文本提取 | 25-50人天 | 50页面 × 0.5-1人天（AIGC替代） |
| 代码改造 | 25-50人天 | 替换硬编码文本 |
| 语言包维护 | 10-15人天 | 文件组织和维护 |
| 翻译校对 | 5-8人天 | 专业翻译和校对 |
| **总计** | **65-123人天** | **约3-6个月工期** |


## 技术方案
+ 引入国际化插件[vue-i18n](https://kazupon.github.io/vue-i18n/zh/)
+ 第三方组件库：引入 HUI 支持vue-i18n国际化，组件兼容
+ il8n-ally vscode 国际化字段检测插件

```plain
// .vscode/settings.json
 "i18n-ally.localesPaths": ["src/locales"],
 "i18n-ally.sortKeys": true,
 "i18n-ally.sourceLanguage": "zh-CN", // 翻译源语言 是你翻译文件的名字
 "i18n-ally.displayLanguage": "en-US", //显示语言， 这里也可以设置显示英文为en
 "i18n-ally.enabledParsers": ["js", "json"],
```

**总体实现方案**：AIGC扫描初步中文键值对文档。开发人员以单文件处理为单位，利用 il8n-ally 识别出需要进行国际化处理的文案。随后按照预设的国际化代码规范，将识别出的文案替换为对应的国际化调用代码，同时补全该文案在国际化文件中的键值对。

## 国际化结构
```plain
src/
├── locales/                    # 国际化语言包目录
│   ├── zh-CN                  # 中文语言包目录（以模块为单位）
│   ├── en-US                  # 英文语言包目录（以模块为单位）
│   ├── zh-CN.js               # 中文语言包
│   ├── en-US.js               # 英文语言包
│   ├── index.js               # 语言包入口文件
│   └── i18n.md                # 国际化文档
├── layouts/
│   └── header/
│       ├── header.vue         # 包含语言切换功能的头部组件
│       └── header.less        # 语言切换相关样式
└── index.js                   # 应用入口，包含i18n配置
```

## 核心配置
### 1. 语言包配置
#### 中文语言包 (`src/locales/zh-CN.js`)
包含以下主要分类：

通用基础类：

+ **common**: 通用文本（按钮、状态、提示等）
+ **menu**: 菜单标题
+ **title**: 页面标题
+ **config**: 通用配置
+ **validation**: 基础校验规则

专业类：

+ **business**: 业务特定文本

按功能模块拆分：

+ **label**: 文本类文案
+ **table**: 表格类文案（可选、business 代替）
+ **message**: 消息提示、接口状态提示、表单验证错误信息
+ **formValidation**: 表单验证错误信息  
...

#### 英文语言包 (`src/locales/en-US.js`)
与中文语言包结构完全一致，提供对应的英文翻译。

#### 注意事项
##### 1. 语言包组织结构
+ **按功能模块分组**：将相关的翻译文本放在同一个对象下
+ **使用嵌套结构**：避免键名过长，使用点号分隔的嵌套结构
+ **保持一致性**：中英文语言包的结构要完全一致

##### 2. 命名规范
+ **使用驼峰命名**：`operationSuccess` 而不是 `operation_success`
+ **语义化命名**：键名要能清楚表达含义
+ **避免重复**：相同含义的文本使用同一个键

#### 语言包内容说明
##### 通用文本 (common)
包含最常用的 UI 文本：

+ 操作按钮：查询、重置、导出、删除、编辑等
+ 状态文本：成功、失败、加载中、运行中等
+ 通用提示：暂无数据、请选择、操作成功等
+ 表格相关：表名、状态、操作、创建时间等
+ 表单占位符：请输入、请选择等

##### 菜单标题 (menu)
+ 通常情况下与页面的标题一致
+ 内部子菜单：  
例如产品服务管理下的子菜单项：eccomDataService：华讯数据服务  
...

##### 页面标题 (title)
各个主要页面的标题：

+ sqlCores: SQL 核心查询
+ managementQuery: 管理查询 API
+ dataObservation: 数据观测  
...

##### 通用配置 (config)
通用配置：暂无全局性配置

##### 基础校验规则（validation）
##### 业务特定文本 (business)
业务特定的文本，如应用观测字段、产品实例、核心实例、数据源等

##### 按功能模块拆分
###### 文本类文案 (label)
当前模块下特有的文案：

###### 表格类文案：
当前模块下表格信息

###### 消息提示 (message)
form 输入提示、文本消息提示、接口状态提示、表单验证错误信息：

+ 错误提示：编辑器内容超过 1M 不支持暂存
+ 确认提示：您确认保存当前编辑器中的内容吗
+ form 输入提示：请输入文件名等
+ 文本消息提示：该模型文件暂无拓扑结构等

###### 表单验证 (formValidation)
表单字段的验证错误信息：

+ 只能输入浮点数格式
+ 邮件地址不正确
+ 只能输入颜色格式等

### 2. 应用入口配置 (`src/index.js`)
```javascript
import VueI18n from "vue-i18n";
// HUI 组件库的语言包
import zhLocale from "h_ui/dist/locale/zh-CN";
import enLocale from "h_ui/dist/locale/en-US";
// 自定义语言包
import customLocales from "@/locales";

Vue.use(VueI18n);

// 合并语言包
const messages = {
  "zh-CN": {
    ...zhLocale,
    ...customLocales["zh-CN"],
  },
  "en-US": {
    ...enLocale,
    ...customLocales["en-US"],
  },
};

// 从本地存储获取语言设置，默认为中文
const savedLanguage = localStorage.getItem("apm-language") || "zh-CN";

// 创建 VueI18n 实例
const i18n = new VueI18n({
  locale: savedLanguage,
  messages,
  silentTranslationWarn: true, // 去掉 warning 提示
});

Vue.locale = () => {}; // 兼容HUI1.0

Vue.use(hui, { i18n: (key, value) => i18n.t(key, value) });

// 在应用启动时挂载i18n实例
const app = hCore({
  extraModelOptions: {
    i18n,
  },
});
```

### 3. 语言切换功能
#### Header 组件 (`src/layouts/header/header.vue`)
在头部组件中添加了语言切换下拉菜单：

```vue
<!-- 语言切换按钮 -->
<div class="language-switcher">
  <h-dropdown trigger="click" placement="bottom-end">
    <div class="language-btn">
      <h-icon name="global" color="#fff" size="16" />
      <span class="current-lang">{{ currentLang === 'zh-CN' ? '中文' : 'EN' }}</span>
      <h-icon name="down" color="#fff" size="12" />
    </div>
    <h-dropdown-menu slot="list">
      <h-dropdown-item @click.native="switchLanguage('zh-CN')" :class="{ active: currentLang === 'zh-CN' }">
        中文
      </h-dropdown-item>
      <h-dropdown-item @click.native="switchLanguage('en-US')" :class="{ active: currentLang === 'en-US' }">
        English
      </h-dropdown-item>
    </h-dropdown-menu>
  </h-dropdown>
</div>

```

切换语言的方法：

```javascript
switchLanguage(lang) {
    this.currentLang = lang;
    if (this.$i18n) {
        this.$i18n.locale = lang;
        // 保存到本地存储
        localStorage.setItem('apm-language', lang);
        // 刷新页面以确保所有组件都更新
        this.$nextTick(() => {
            window.location.reload();
        });
    }
}
```

## 使用方法
### 1. 在 Vue 模板中使用
```vue
<template>
  <div>
    <!-- 基本用法 -->
    <h-button>{{ $t("common.query") }}</h-button>
    <!-- 在属性中使用 -->
    <h-input :placeholder="$t('common.placeholder.input')" />

    <!-- 表格列配置 -->
    <h-table :columns="columns" :data="tableData" />
  </div>
</template>
<script>
export default {
  computed: {
    columns() {
      return [
        {
          title: this.$t("common.tableName"),
          key: "tableName",
        },
        {
          title: this.$t("common.status"),
          key: "status",
        },
        {
          title: this.$t("common.operation"),
          key: "operation",
        },
      ];
    },
  },
};
</script>

```

### 2. 在 JavaScript 中使用
```javascript
export default {
  methods: {
    handleSave() {
      this.$hMessage.success(this.$t("common.saveSuccess"));
    },

    handleDelete() {
      this.$hMsgBox.confirm({
        title: this.$t("common.confirmDelete"),
        onOk: () => {
          this.$hMessage.success(this.$t("common.deleteSuccess"));
        },
      });
    },
  },
};
```

### 3. 在渲染函数中使用
```javascript
export default {
  render() {
    return (
      <div class="no-data">
        <img src={this.imgSrc} alt={this.$t("message.imageCannotDisplay")} />
        <div class="text">{this.text || this.$t("common.noData")}</div>
      </div>
    );
  },
};
```

### 4. 组件国际化
举例：

```javascript
// ✅ 推荐：支持自定义文本，同时提供国际化默认值
export default {
  props: {
    text: {
      type: String,
      default: "",
    },
  },
  computed: {
    displayText() {
      return this.text || this.$t("common.noData");
    },
  },
};
```

### 5. 错误处理
```javascript
// 安全的国际化调用
const getMessage = (key, fallback) => {
  try {
    return this.$t ? this.$t(key) : fallback;
  } catch (error) {
    console.warn(`Translation key "${key}" not found`);
    return fallback;
  }
};
```

### 6. 参数插值
```javascript
 this.$t('common.selectedCount', { count: 5 })
 // 对应的文案：'已选择 {count} 项'
```

## 扩展指南
### 1. 添加新的翻译文本
在对应的语言包文件中添加新的键值对：

```javascript
// zh-CN.js
export default {
    // 现有内容...
    newModule: {
        newText: '新的中文文本'
    }
};

// en-US.js
export default {
    // 现有内容...
    newModule: {
        newText: 'New English Text'
    }
};
```

### 2. 在组件中使用新文本
```vue
<template>
  <div>{{ $t("newModule.newText") }}</div>
</template>

```

### 3. 添加新语言
1. 创建新语言包文件（如 `ja-JP.js`）
2. 在 `index.js` 中导入并导出
3. 在应用入口文件中注册
4. 在语言切换组件中添加选项



## 其他国际化事项
除了字段翻译之外，国际化还需要注意以下重要问题：

### 1. 日期时间格式化
不同地区的日期时间格式存在显著差异：

**中文格式**：

+ 日期：2024-01-15 或 2024年1月15日
+ 时间：14:30:05
+ 日期时间：2024-01-15 14:30:05
+ 星期：星期一、星期二...

**英文格式**：

+ 日期：01/15/2024 或 Jan 15, 2024
+ 时间：2:30:05 PM
+ 日期时间：01/15/2024 2:30:05 PM
+ 星期：Monday, Tuesday...

**解决方案**：

```javascript
// 根据语言类型格式化日期
export function formatDate(date, locale = getCurrentLocale()) {
    const formats = {
        'zh-CN': 'YYYY-MM-DD',
        'en-US': 'MM/DD/YYYY'
    };
    return dayjs(date).format(formats[locale]);
}

// 在组件中使用
computed: {
    formattedDate() {
        return this.$formatDate(this.date);
    }
}
```

### 2. 数字格式化
不同地区的数字、货币、百分比格式不同：

**数字分隔符**：

+ 中文：1,234,567.89
+ 欧洲：1.234.567,89

**货币格式**：

+ 中文：¥1,234.56
+ 英文：$1,234.56
+ 欧元：€1.234,56

**解决方案**：

```javascript
// 使用 Intl.NumberFormat API
export function formatNumber(number, locale = getCurrentLocale()) {
    return new Intl.NumberFormat(locale).format(number);
}

export function formatCurrency(amount, currency = 'CNY', locale = getCurrentLocale()) {
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency
    }).format(amount);
}
```

### 3. 文本方向和布局
**从左到右 (LTR) vs 从右到左 (RTL)**：

+ 中文、英文：从左到右
+ 阿拉伯语、希伯来语：从右到左

**解决方案**：

```css
/* 支持 RTL 布局 */
.container {
    direction: ltr; /* 默认 LTR */
}

[dir="rtl"] .container {
    direction: rtl;
}

/* 使用逻辑属性 */
.element {
    margin-inline-start: 10px; /* 替代 margin-left */
    margin-inline-end: 20px;   /* 替代 margin-right */
}
```

### 4. 字体和字符编码
**字体支持**：

+ 中文：需要支持中文字体
+ 英文：拉丁字符集
+ 阿拉伯语：阿拉伯字符集

**解决方案**：

```css
/* 多语言字体栈 */
.text {
    font-family:
        "PingFang SC",     /* 中文 */
        "Helvetica Neue",  /* 英文 */
        "Arial",
        sans-serif;
}

/* 根据语言设置字体 */
[lang="zh-CN"] {
    font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

[lang="en-US"] {
    font-family: "Helvetica Neue", "Arial", sans-serif;
}
```

### 5. 文本长度和界面适配
不同语言的文本长度差异很大：

**文本长度变化**：

+ 德语通常比英语长 30%
+ 中文通常比英语短
+ 阿拉伯语可能需要更多垂直空间

**解决方案**：

```css
/* 弹性布局适配文本长度 */
.button {
    min-width: 80px;
    padding: 8px 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式文本容器 */
.text-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
```

### 6. 图片和图标国际化
**包含文字的图片**：

+ 需要为不同语言准备不同版本
+ 考虑使用 SVG 和文字分离

**图标含义**：

+ 某些图标在不同文化中含义不同
+ 颜色在不同文化中的含义不同

**解决方案**：

```javascript
// 根据语言加载不同图片
computed: {
    logoSrc() {
        const locale = this.$i18n.locale;
        return require(`@/assets/images/logo-${locale}.png`);
    }
}
```

### 7. 输入法和键盘支持
**输入方式差异**：

+ 中文：拼音输入法
+ 日文：假名输入法
+ 阿拉伯语：从右到左输入

**解决方案**：

```html
<!-- 设置输入法模式 -->
<input
    type="text"
    :lang="$i18n.locale"
    :dir="isRTL ? 'rtl' : 'ltr'"
/>
```

### 8. 时区处理
**时区显示**：

+ 服务器时间 vs 本地时间
+ 不同地区的时区偏移

**解决方案**：

```javascript
// 时区转换
export function formatTimeWithTimezone(date, timezone, locale) {
    return new Intl.DateTimeFormat(locale, {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).format(new Date(date));
}
```

### 9. 排序和搜索
**字符排序规则**：

+ 中文：拼音排序
+ 英文：字母排序
+ 其他语言：各自的排序规则

**解决方案**：

```javascript
// 使用 Intl.Collator 进行本地化排序
export function sortByLocale(array, key, locale = getCurrentLocale()) {
    const collator = new Intl.Collator(locale);
    return array.sort((a, b) => collator.compare(a[key], b[key]));
}
```

### 10. 表单验证
**验证规则差异**：

+ 电话号码格式
+ 邮政编码格式
+ 身份证号格式

**解决方案**：

```javascript
// 根据地区设置验证规则
const validationRules = {
    'zh-CN': {
        phone: /^1[3-9]\d{9}$/,
        postcode: /^\d{6}$/
    },
    'en-US': {
        phone: /^\(\d{3}\) \d{3}-\d{4}$/,
        postcode: /^\d{5}(-\d{4})?$/
    }
};

export function validatePhone(phone, locale = getCurrentLocale()) {
    const rule = validationRules[locale]?.phone;
    return rule ? rule.test(phone) : true;
}
```

### 11. 性能优化
**按需加载语言包**：

```javascript
// 动态导入语言包
async function loadLocale(locale) {
    const messages = await import(`@/locales/${locale}.js`);
    this.$i18n.setLocaleMessage(locale, messages.default);
}
```

**缓存翻译结果**：

```javascript
// 翻译缓存
const translationCache = new Map();

export function cachedTranslate(key, locale) {
    const cacheKey = `${locale}:${key}`;
    if (translationCache.has(cacheKey)) {
        return translationCache.get(cacheKey);
    }

    const result = translate(key, locale);
    translationCache.set(cacheKey, result);
    return result;
}
```

### 12. 测试策略
**多语言测试**：

+ 界面布局测试
+ 文本截断测试
+ 功能完整性测试
+ 性能测试

**自动化测试**：

```javascript
// 多语言 E2E 测试
describe('Multi-language support', () => {
    ['zh-CN', 'en-US'].forEach(locale => {
        it(`should work correctly in ${locale}`, () => {
            cy.visit(`/?lang=${locale}`);
            cy.get('[data-testid="welcome-text"]')
              .should('contain', getExpectedText(locale));
        });
    });
});
```

### 13. SEO 和可访问性
**搜索引擎优化**：

+ 设置正确的 `lang` 属性
+ 使用 `hreflang` 标签
+ 多语言 URL 结构

**可访问性支持**：

+ 屏幕阅读器支持
+ 键盘导航
+ 高对比度模式

**解决方案**：

```html
<!-- HTML 语言属性 -->
<html :lang="$i18n.locale">

<!-- 多语言链接 -->
<link rel="alternate" hreflang="zh-CN" href="/zh-cn/page" />
<link rel="alternate" hreflang="en-US" href="/en-us/page" />

<!-- 可访问性属性 -->
<button :aria-label="$t('common.close')" @click="close">
    <i class="icon-close"></i>
</button>

```

### 14. 数据格式化
**项目中的具体应用**：

基于项目现有的时间格式化函数，建议创建统一的国际化格式化工具：

```javascript
// src/utils/dateI18n.js
import { getCurrentLocale } from '@/utils/i18n';

// 替换现有的 formatDate 函数
export function formatDate(date, locale = getCurrentLocale()) {
    if (!date) return '';

    const d = new Date(date);
    if (d.toString().toLowerCase() === 'invalid date') return '';

    const formats = {
        'zh-CN': 'YYYY-MM-DD',
        'en-US': 'MM/DD/YYYY'
    };

    // 使用现有的 dateFormat 函数逻辑
    return dateFormat(date, formats[locale]);
}

// 替换现有的 formatTimeAgo 函数
export function formatTimeAgo(timestamp, locale = getCurrentLocale()) {
    if (!timestamp) return '0';

    const currentTime = Math.floor(Date.now() / 1000);
    const timeDifference = Math.max(currentTime - timestamp, 0);

    const units = {
        'zh-CN': {
            second: '秒前', minute: '分钟前',
            hour: '小时前', day: '天前'
        },
        'en-US': {
            second: 'second ago', minute: 'minute ago',
            hour: 'hour ago', day: 'day ago'
        }
    };

    // 现有逻辑 + 多语言支持
    // ...
}
```

###  15.使用安全的国际化混入
为了避免 i18n 相关错误，可以使用提供的安全混入：

```javascript
// src/mixins/i18n.js
import i18nMixin from "@/mixins/i18n";

export default {
  mixins: [i18nMixin],
  methods: {
    handleClick() {
      // 使用安全的翻译方法
      this.$hMessage.success(
        this.$safeT("common.operationSuccess", {}, "操作成功")
      );
    },
  },
};
```

## 总结
对于前端来说是一个国际化是前期投入大，长期收益显著的工程。具体实现需先运行扫描工具评估现状，制定分阶段实施计划，确保在可控成本内实现目标。

### 项目支持
+ **技术栈**: Vue.js + vue-i18n + HUI组件库
+ **页面规模**: 50个路由页面需要国际化改造
+ **配置工具**: i18n-ally VSCode插件
+ **语言支持**: 中文(zh-CN) + 英文(en-US)

### 优点
+ **代码解耦**: 文本与业务逻辑分离
+ **可维护性**: 统一管理所有文本内容
+ **可扩展性**: 新增语言只需添加语言包

### 难点
### 1. 实施挑战
+ **工作量可控**: 65-123人天，约3-6个月完成

```javascript
// 硬编码文本替换
// 原始: <h-button>查询数据</h-button>
// 改造: <h-button>{{ $t('common.queryData') }}</h-button>
// 动态内容处理
// 错误: '共找到' + count + '条记录'
// 正确: this.$t('common.foundRecords', { count })
```

### 3. UI布局挑战
+ **文本长度变化**: 中文"查询"vs英文"Query Data"
+ **日期格式差异**: 中文"2024-01-15" vs 英文"01/15/2024"
+ **响应式适配**: 不同语言文本长度对布局的影响

### 4. 维护复杂性
+ **命名规范**: 建立清晰的翻译键命名
+ **质量控制**: 术语一致性、上下文准确性
+ **版本同步**: 新功能开发时同步更新翻译

### 5.质量保证
+ 专业术语库支持
+ 了解金融知识的语言专家：技术审核 + 语言审核。
+ 用户测试和反馈收集

