/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-01 16:23:30
 * @LastEditTime: 2023-05-05 11:02:07
 * @LastEditors: <PERSON><PERSON> Ying
 */
import hCore, { store } from 'hui-core';
import './reset.css';
import './color.css';
import hui from 'h_ui';
import Table from '@hui/table';
import '@hui/table/dist/table.css';
import 'h_ui/dist/h_ui.min.css';
import '@/assets/css/main.less';
import routes from './router/router';
import Vue from 'vue';
import { importFontFace } from '@/utils/utils.js';
import GlobalMethods from '@/global.js'; // 引入全局变量、全局方法文件

import VueI18n from 'vue-i18n';
// HUI 组件库的语言包
import zhLocale from 'h_ui/dist/locale/zh-CN';
import enLocale from 'h_ui/dist/locale/en-US';
// 自定义语言包
import customLocales from '@/locales';

Vue.use(VueI18n);

// 合并语言包
const messages = {
    'zh-CN': {
        ...zhLocale,
        ...customLocales['zh-CN']
    },
    'en-US': {
        ...enLocale,
        ...customLocales['en-US']
    }
};

// 从本地存储获取语言设置，默认为中文
const savedLanguage = localStorage.getItem('apm-language') || 'zh-CN';

// 创建 VueI18n 实例
const i18n = new VueI18n({
    locale: savedLanguage, // 设置默认语言
    messages, // 设置语言包
    silentTranslationWarn: true // 去掉 warning 提示
});

// 兼容HUI1.0，Vue.use(VueI18n) 有$t方法后 ,默认设置语言报错
Vue.locale = () => {};

// 配置HUI的国际化
Vue.use(hui, {
    i18n: (key, value) => i18n.t(key, value),
    locale: savedLanguage
});

Vue.use(Table);

// 使用全局变量和全局方法
Vue.use(GlobalMethods);

Vue.config.productionTip = false;

const app = hCore({
    extraModelOptions: {
        store,
        i18n
    }
});

app.addRoutes(routes);

app.start((vue) => {
    importFontFace(vue);
});
