const fs = require('fs');
const path = require('path');

// 中文字符正则表达式
const CHINESE_REGEX = /[\u4e00-\u9fff]+/g;

// 路由映射表
const ROUTE_MAPPING = {
    'analyseConfig': 'index-analyseConfig',
    'analyseData': 'index-analyseData', 
    'appMonitor': 'index-appMonitor',
    'businessMonitor': 'index-businessMonitor',
    'ldpDataObservation': 'index-ldpDataObservation',
    'ldpLinkConfig': 'index-ldpLinkConfig',
    'ldpTable': 'index-ldpTable',
    'sqlTable': 'index-sqlTable',
    'sqlCores': 'index-sqlCores',
    'managementQuery': 'index-managementQuery',
    'mdbDataObservation': 'index-mdbDataObservation',
    'marketAllLink': 'index-marketAllLink',
    'marketMonitor': 'index-marketMonitor',
    'marketNodeDelayList': 'index-marketNodeDelayList',
    'marketPenetrateList': 'index-marketPenetrateList',
    'marketTimeDelay': 'index-marketTimeDelay',
    'monitor': 'index-monitor',
    'ldpMonitor': 'ldp-monitor',
    'noticeManagerList': 'index-noticeManagerList',
    'productDataStorage': 'index-productDataStorage',
    'productTimeDetail': 'index-productTimeDetail',
    'productTimeSummary': 'index-productTimeSummary',
    'smsList': 'index-smsList',
    'transaction': 'index-transaction',
    'rcmDeploy': 'index-rcmDeploy',
    'rcmBacklogMonitor': 'index-rcmBacklogMonitor',
    'mcDeploy': 'index-mcDeploy',
    'mcDataObservation': 'index-mcDataObservation',
    'brokerDataLimit': 'index-brokerDataLimit',
    'ustTableVerification': 'index-ustTableVerification',
    'coreReplayObservation': 'index-ccoreReplayObservation',
    'mdbPrivilegeManage': 'index-mdbPrivilegeManage',
    'networkSendAndRecevied': 'index-networkSendAndRecevied',
    'productServiceList': 'index-productServiceList',
    'tripartiteServiceList': 'index-tripartiteServiceList',
    'latencyTrendAnalysis': 'index-latencyTrendAnalysis',
    'topoMonitor': 'index-topoMonitor',
    'appRunningState': 'index-appRunningState',
    'accordMonitor': 'index-accordMonitor',
    'accordObservation': 'index-accordObservation',
    'rcmObservation': 'index-rcmObservation',
    'locateConfig': 'index-locateConfig',
    'ldpLogCenter': 'index-ldpLogCenter',
    'threadInfoOverview': 'index-threadInfoOverview',
    'coreFuncHandleObservation': 'index-coreFuncHandleObservation',
    'createRule': 'index-createRule',
    'emergencyManagementConfig': 'index-emergencyManagementConfig',
    'dataSecondAppearance': 'index-dataSecondAppearance',
    'mdbDataExport': 'index-mdbDataExport',
    'createMdbDataExport': 'index-createMdbDataExport'
};

// 扫描结果
const scanResults = {};

// 递归扫描目录
function scanDirectory(dirPath, basePath = '') {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            scanDirectory(filePath, path.join(basePath, file));
        } else if (file.endsWith('.vue')) {
            scanVueFile(filePath, basePath, file);
        }
    });
}

// 扫描Vue文件中的中文文案
function scanVueFile(filePath, basePath, fileName) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.join(basePath, fileName).replace(/\\/g, '/');
    
    const routeName = getRouteNameFromPath(relativePath);
    
    if (!scanResults[routeName]) {
        scanResults[routeName] = {
            path: relativePath,
            texts: new Set()
        };
    }
    
    extractPureChineseTexts(content, scanResults[routeName].texts);
}

// 从文件路径提取路由名称
function getRouteNameFromPath(filePath) {
    if (filePath.startsWith('views/index/')) {
        const fileName = path.basename(filePath, '.vue');
        return ROUTE_MAPPING[fileName] || fileName;
    }
    
    if (filePath.startsWith('components/')) {
        const parts = filePath.split('/');
        return parts[1] || 'common';
    }
    
    return 'common';
}

// 提取纯净的中文文案
function extractPureChineseTexts(content, textSet) {
    // 1. 提取HTML标签之间的纯文本中文
    const htmlTextRegex = />([^<{]*[\u4e00-\u9fff][^<{]*)</g;
    let match;
    while ((match = htmlTextRegex.exec(content)) !== null) {
        const text = match[1].trim();
        if (isValidChineseText(text)) {
            textSet.add(text);
        }
    }
    
    // 2. 提取HTML属性中的中文
    const attrRegex = /(placeholder|title|label|content|text|alt|aria-label)\s*=\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/gi;
    while ((match = attrRegex.exec(content)) !== null) {
        const text = match[2].trim();
        if (isValidChineseText(text)) {
            textSet.add(text);
        }
    }
    
    // 3. 提取JavaScript字符串中的中文（更严格的过滤）
    const jsStringRegex = /['"`]([^'"`\n\r]*[\u4e00-\u9fff][^'"`\n\r]*)['"`]/g;
    while ((match = jsStringRegex.exec(content)) !== null) {
        const text = match[1].trim();
        if (isValidChineseText(text) && isPureUIText(text)) {
            textSet.add(text);
        }
    }
    
    // 4. 提取Vue插值表达式中的中文字符串
    const interpolationRegex = /\{\{\s*['"`]([^'"`]*[\u4e00-\u9fff][^'"`]*)['"`]\s*\}\}/g;
    while ((match = interpolationRegex.exec(content)) !== null) {
        const text = match[1].trim();
        if (isValidChineseText(text)) {
            textSet.add(text);
        }
    }
}

// 判断是否为有效的中文文本
function isValidChineseText(text) {
    if (!text || text.length === 0 || text.length > 100) return false;
    if (!CHINESE_REGEX.test(text)) return false;
    
    // 排除包含特殊字符的文本
    const excludePatterns = [
        /\{\{|\}\}/, // Vue插值
        /\$\{/, // 模板字符串
        /import|export|function|console|localStorage|this\.|window\./, // 代码关键字
        /http|www\.|\.com|\.cn/, // URL
        /\r|\n/, // 换行符
        /^\s*\/\/|^\s*\*/, // 注释
        /class=|style=|v-|:/, // Vue指令
        /\w+\(\)/, // 函数调用
        /\d{4}-\d{2}-\d{2}/, // 日期格式
        /[a-zA-Z]{10,}/ // 长英文字符串
    ];
    
    return !excludePatterns.some(pattern => pattern.test(text));
}

// 判断是否为纯UI文本（排除代码片段）
function isPureUIText(text) {
    // 排除明显的代码片段
    const codePatterns = [
        /\w+\.\w+/, // 对象属性访问
        /\[\w+\]/, // 数组访问
        /\w+\(/, // 函数调用
        /=\s*\w+/, // 赋值语句
        /\w+:\s*\w+/, // 对象属性
        /\w+\s*\?\s*\w+/, // 三元运算符
        /\w+\s*&&\s*\w+/, // 逻辑运算符
        /\w+\|\|\w+/, // 或运算符
        /new\s+\w+/, // new 关键字
        /return\s+/, // return 语句
        /const\s+|let\s+|var\s+/, // 变量声明
        /if\s*\(|for\s*\(|while\s*\(/, // 控制语句
        /\w+\s*=>\s*/, // 箭头函数
        /\.\w+\(/, // 方法调用
        /\w+\[\d+\]/ // 数组索引
    ];
    
    return !codePatterns.some(pattern => pattern.test(text));
}

// 生成语义化的键名
function generateSemanticKey(text) {
    const keywordMap = {
        '请选择': 'pleaseSelect',
        '请输入': 'pleaseInput',
        '请输入搜索内容': 'pleaseInputSearchContent',
        '查询': 'query',
        '搜索': 'search',
        '重置': 'reset',
        '导出': 'export',
        '删除': 'delete',
        '编辑': 'edit',
        '保存': 'save',
        '取消': 'cancel',
        '确认': 'confirm',
        '提交': 'submit',
        '关闭': 'close',
        '返回': 'back',
        '刷新': 'refresh',
        '清空': 'clear',
        '选择': 'select',
        '全选': 'selectAll',
        '配置': 'config',
        '管理': 'management',
        '监控': 'monitor',
        '观测': 'observation',
        '数据': 'data',
        '应用': 'application',
        '节点': 'node',
        '实例': 'instance',
        '产品': 'product',
        '核心': 'core',
        '集群': 'cluster',
        '链路': 'link',
        '日志': 'log',
        '时间': 'time',
        '状态': 'status',
        '成功': 'success',
        '失败': 'failed',
        '错误': 'error',
        '警告': 'warning',
        '信息': 'info',
        '加载中': 'loading',
        '暂无数据': 'noData',
        '操作成功': 'operationSuccess',
        '操作失败': 'operationFailed',
        '开始时间': 'startTime',
        '结束时间': 'endTime',
        '时间范围': 'timeRange',
        '今天': 'today',
        '昨天': 'yesterday',
        '本周': 'thisWeek',
        '本月': 'thisMonth',
        '详情': 'detail',
        '添加': 'add',
        '新建': 'create',
        '修改': 'modify',
        '更新': 'update',
        '下载': 'download',
        '上传': 'upload',
        '导入': 'import',
        '批量': 'batch',
        '快捷': 'quick',
        '总览': 'overview',
        '列表': 'list',
        '表格': 'table',
        '图表': 'chart',
        '统计': 'statistics',
        '分析': 'analysis',
        '报告': 'report',
        '设置': 'settings',
        '帮助': 'help',
        '关于': 'about',
        '暂无': 'none',
        '未知': 'unknown',
        '全部': 'all',
        '部分': 'partial',
        '完成': 'complete',
        '进行中': 'inProgress',
        '待处理': 'pending',
        '已停止': 'stopped',
        '运行中': 'running',
        '已暂停': 'paused'
    };
    
    // 查找完全匹配的关键词
    if (keywordMap[text]) {
        return keywordMap[text];
    }
    
    // 查找包含关键词的文本
    for (const [keyword, key] of Object.entries(keywordMap)) {
        if (text.includes(keyword)) {
            return key;
        }
    }
    
    // 生成基于内容的键名
    const cleanText = text.replace(/[^\u4e00-\u9fff\w]/g, '');
    if (cleanText.length <= 6) {
        return `text_${cleanText}`;
    }
    
    return `text_${cleanText.substring(0, 6)}`;
}

// 生成国际化键值对
function generateI18nKeys(routeName, texts) {
    const keys = {};
    const textArray = Array.from(texts).sort();
    
    textArray.forEach((text) => {
        const key = generateSemanticKey(text);
        keys[key] = text;
    });
    
    return keys;
}

// 主函数
function main() {
    console.log('开始精确扫描项目中的中文文案...');
    
    // 扫描views目录
    if (fs.existsSync('./src/views')) {
        scanDirectory('./src/views', 'views');
    }
    
    // 扫描components目录
    if (fs.existsSync('./src/components')) {
        scanDirectory('./src/components', 'components');
    }
    
    // 生成报告
    const report = generateReport();
    
    // 输出结果
    fs.writeFileSync('./precise-i18n-scan-report.json', JSON.stringify(report, null, 2), 'utf-8');
    console.log('扫描完成！结果已保存到 precise-i18n-scan-report.json');
    
    // 输出统计信息
    console.log(`\n扫描统计：`);
    console.log(`- 扫描的路由页面数: ${Object.keys(report.routes).length}`);
    console.log(`- 发现的中文文案总数: ${report.summary.totalTexts}`);
    console.log(`- 生成的国际化键值对数: ${report.summary.totalKeys}`);
}

// 生成报告
function generateReport() {
    const routes = {};
    let totalTexts = 0;
    let totalKeys = 0;
    
    Object.keys(scanResults).forEach(routeName => {
        const result = scanResults[routeName];
        const texts = Array.from(result.texts);
        const i18nKeys = generateI18nKeys(routeName, result.texts);
        
        routes[routeName] = {
            path: result.path,
            texts: texts,
            i18nKeys: i18nKeys,
            count: texts.length
        };
        
        totalTexts += texts.length;
        totalKeys += Object.keys(i18nKeys).length;
    });
    
    return {
        summary: {
            totalRoutes: Object.keys(routes).length,
            totalTexts: totalTexts,
            totalKeys: totalKeys,
            scanTime: new Date().toISOString()
        },
        routes: routes
    };
}

// 运行扫描
if (require.main === module) {
    main();
}

module.exports = {
    scanDirectory,
    scanVueFile,
    extractPureChineseTexts,
    generateI18nKeys,
    generateReport
};
