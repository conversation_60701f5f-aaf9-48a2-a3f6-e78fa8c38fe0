# 项目中文文案国际化扫描报告

## 扫描概览

本次扫描对项目中的所有Vue页面进行了全面的中文文案提取，并按路由维度生成了对应的国际化键值对。

### 扫描统计

- **扫描时间**: 2025-07-31
- **总路由页面数**: 95个
- **发现的中文文案总数**: 2,597个
- **生成的国际化键值对数**: 1,778个
- **按功能分组数**: 15个
- **未分组路由数**: 52个

## 路由分组统计

### 1. 管理相关 (management)
- **路由数**: 6个
- **文案数**: 98个
- **键值对数**: 66个
- **平均每路由文案数**: 16个
- **主要页面**:
  - `index-smsList`: 33个文案 (短信通知列表)
  - `index-mdbPrivilegeManage`: 28个文案 (MDB权限管理)
  - `index-managementQuery`: 19个文案 (管理查询)

### 2. 监控相关 (monitor)
- **路由数**: 10个
- **文案数**: 88个
- **键值对数**: 75个
- **平均每路由文案数**: 9个
- **主要页面**:
  - `index-marketPenetrateList`: 21个文案 (市场穿透列表)
  - `index-businessMonitor`: 11个文案 (业务监控)
  - `index-appRunningState`: 11个文案 (应用运行状态)

### 3. 表格查询相关 (table)
- **路由数**: 3个
- **文案数**: 41个
- **键值对数**: 37个
- **平均每路由文案数**: 14个
- **主要页面**:
  - `index-sqlTable`: 25个文案 (SQL表格)
  - `index-sqlCores`: 12个文案 (SQL核心)
  - `index-ldpTable`: 4个文案 (LDP表格)

### 4. 数据存储相关 (storage)
- **路由数**: 3个
- **文案数**: 44个
- **键值对数**: 32个
- **平均每路由文案数**: 15个
- **主要页面**:
  - `index-productTimeDetail`: 15个文案 (产品时间详情)
  - `index-productTimeSummary`: 15个文案 (产品时间汇总)
  - `index-productDataStorage`: 14个文案 (产品数据存储)

### 5. 规则相关 (rule)
- **路由数**: 1个
- **文案数**: 36个
- **键值对数**: 27个
- **平均每路由文案数**: 36个
- **主要页面**:
  - `index-createRule`: 36个文案 (创建规则)

### 6. 分析工具 (tools)
- **路由数**: 2个
- **文案数**: 25个
- **键值对数**: 23个
- **平均每路由文案数**: 13个
- **主要页面**:
  - `index-latencyTrendAnalysis`: 23个文案 (时延趋势分析)
  - `index-dataSecondAppearance`: 2个文案 (数据二次出现)

### 7. 分析相关 (analysis)
- **路由数**: 2个
- **文案数**: 23个
- **键值对数**: 19个
- **平均每路由文案数**: 12个
- **主要页面**:
  - `index-analyseData`: 18个文案 (分析数据)
  - `index-analyseConfig`: 5个文案 (分析配置)

### 8. 交易相关 (transaction)
- **路由数**: 1个
- **文案数**: 15个
- **键值对数**: 10个
- **平均每路由文案数**: 15个
- **主要页面**:
  - `index-transaction`: 15个文案 (交易)

### 9. 观测相关 (observation)
- **路由数**: 5个
- **文案数**: 17个
- **键值对数**: 17个
- **平均每路由文案数**: 3个
- **主要页面**:
  - `index-ldpDataObservation`: 6个文案 (LDP数据观测)
  - `index-rcmObservation`: 4个文案 (RCM观测)

### 10. 配置相关 (config)
- **路由数**: 3个
- **文案数**: 11个
- **键值对数**: 7个
- **平均每路由文案数**: 4个
- **主要页面**:
  - `index-ldpLinkConfig`: 6个文案 (LDP链路配置)
  - `apmMonitorConfig`: 3个文案 (APM监控配置)

## 生成的文件说明

### 1. 扫描结果文件
- `precise-i18n-scan-report.json`: 完整的扫描结果，包含所有页面的中文文案和键值对
- `route-based-i18n-report.json`: 按路由分组的完整报告
- `i18n-statistics.json`: 详细的统计信息

### 2. 分组国际化文件
为每个功能分组生成了两种格式的国际化文件：

#### Vue i18n格式 (i18n-{group}-vue.json)
```json
{
  "index-managementQuery": {
    "close": "关闭全部",
    "data": "其他数据",
    "query": "输入节点名查询",
    "export": "支持批量导出当前产品下的所有管理功能数据"
  }
}
```

#### 扁平化格式 (i18n-{group}-flat.json)
```json
{
  "index-managementQuery.close": "关闭全部",
  "index-managementQuery.data": "其他数据",
  "index-managementQuery.query": "输入节点名查询"
}
```

## 键名命名规范

生成的国际化键名遵循以下规范：

### 1. 语义化键名
- `pleaseSelect`: 请选择
- `pleaseInput`: 请输入
- `query`: 查询
- `search`: 搜索
- `export`: 导出
- `delete`: 删除
- `edit`: 编辑
- `save`: 保存
- `cancel`: 取消
- `confirm`: 确认
- `config`: 配置
- `management`: 管理
- `monitor`: 监控
- `data`: 数据
- `application`: 应用
- `node`: 节点
- `instance`: 实例

### 2. 通用键名
对于无法匹配语义化键名的文案，使用 `text_` 前缀加内容摘要的方式命名。

## 使用建议

### 1. 集成到现有国际化系统
1. 将生成的Vue格式文件集成到现有的 `src/locales/zh-CN.js` 中
2. 按功能模块组织国际化文案，便于维护
3. 为英文版本创建对应的翻译文件

### 2. 代码替换
1. 使用生成的键值对替换代码中的硬编码中文文案
2. 优先处理文案数量较多的页面，如管理查询、短信列表等
3. 建议分批次进行替换，降低风险

### 3. 维护建议
1. 建立文案审核流程，确保新增文案及时添加到国际化文件
2. 定期运行扫描脚本，检查是否有遗漏的中文文案
3. 为常用文案建立统一的键名标准

## 扫描脚本说明

本次扫描使用了两个主要脚本：

1. **precise-i18n-scanner.js**: 精确扫描Vue文件中的中文文案
   - 过滤代码片段，只提取纯净的UI文案
   - 支持模板、属性、JavaScript字符串等多种场景
   - 自动生成语义化的键名

2. **generate-i18n-files.js**: 生成按路由分组的国际化文件
   - 按功能模块对路由进行分组
   - 生成多种格式的国际化文件
   - 提供详细的统计信息

## 总结

本次扫描成功识别了项目中的2,597个中文文案，生成了1,778个国际化键值对，为项目的国际化改造提供了完整的基础数据。建议按照功能模块逐步进行国际化改造，优先处理文案数量较多的核心功能模块。
